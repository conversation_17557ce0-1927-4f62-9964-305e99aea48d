# 项目结构与组织

## 根目录结构
```
├── docs/                    # VitePress 源内容
├── js-scripts/             # JavaScript 自动化脚本
├── python-scripts/         # Python 数据处理脚本
├── B站付费课程合集-数据/    # 原始数据文件 (Excel, JSON)
├── 数据/                   # 额外数据目录
├── node_modules/           # 依赖包
├── package.json           # 项目配置
├── vercel.json           # 部署配置
└── README.md             # 项目文档
```

## 内容结构 (`docs/`)
```
docs/
├── .vitepress/
│   ├── config.mjs         # VitePress 配置
│   ├── dist/             # 构建输出
│   └── cache/            # 构建缓存
├── public/               # 静态资源 (图标, 图片)
├── index.md             # 首页
├── about.md             # 关于页面
├── websites/            # 网站资源板块
├── games/               # 游戏资源板块
│   ├── pc/             # PC 游戏
│   └── android/        # 安卓游戏
├── videos/             # 视频资源板块
│   └── bilibili/       # B站课程
└── cloud/              # 网盘资源板块
```

## 内容规范

### 文件命名
- 使用 kebab-case 命名文件: `example-file-name.md`
- 中文内容使用拼音或英文对应词
- 分类页面使用索引文件: `index.md`

### Frontmatter 结构
每个内容页面都应包含：
```yaml
---
title: SEO 优化的页面标题
description: 用于 meta 标签的页面描述
head:
  - - meta
    - name: keywords
      content: 相关,关键词,列表
  - - script
    - type: application/ld+json
    - |
      { 结构化数据对象 }
---
```

### 导航结构
- **层级分类** - 清晰的父子关系
- **共享侧边栏** - 相关板块间共享 (游戏/视频/网盘)
- **面包屑导航** - 通过结构化数据实现

## 自动化脚本

### JavaScript 脚本 (`js-scripts/`)
- 数据处理和转换
- 内容生成和更新
- SEO 优化任务
- 链接验证和修复

### Python 脚本 (`python-scripts/`)
- 数据分析和验证
- 文件系统操作
- 内容迁移任务
- 质量保证检查

## 数据管理
- **原始数据** 存储在专用目录
- **JSON/Excel 格式** 用于结构化数据
- **自动化处理** 生成 markdown 内容
- **版本控制** 确保数据一致性

## 资源组织
- **图标和 favicon** 存放在 `docs/public/`
- **图片** 按分类组织在 `docs/public/images/`
- **静态文件** 直接从 public 目录提供服务

## URL 结构
- 简洁 URL，无 `.html` 扩展名
- 无尾部斜杠
- 逻辑层级匹配内容结构
- SEO 友好的英文/拼音路径