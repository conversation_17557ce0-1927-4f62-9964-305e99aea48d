# 技术栈与构建系统

## 框架与核心技术
- **VitePress 1.6.3** - 文档站点静态生成器
- **Node.js** - 运行时环境
- **Markdown** - 主要内容格式
- **JavaScript/ES6** - 配置和脚本编写

## 构建系统
VitePress 通过以下 npm 脚本处理构建过程：

```bash
# 开发服务器
npm run docs:dev

# 生产构建
npm run docs:build

# 预览生产构建
npm run docs:preview
```

## 部署配置
- **Vercel** - 托管平台，支持自动部署
- **域名**: xmtx.org
- 启用简洁 URL（无 .html 扩展名）
- 禁用尾部斜杠

## SEO 与分析
- 全面的 meta 标签和 Open Graph 支持
- 结构化数据 (JSON-LD) 优化搜索索引
- Google Analytics (G-QP4DG5B7BR)
- Umami Analytics 隐私友好追踪
- 自动生成站点地图
- 规范 URL 和正确的重定向

## 内容管理
- **Excel/JSON 数据处理** - 使用 `xlsx` 库进行数据导入
- **自动化脚本** - 支持 JavaScript 和 Python
- **基于 Frontmatter** 的页面配置
- **分类导航** - 动态侧边栏生成

## 关键配置文件
- `docs/.vitepress/config.mjs` - VitePress 主配置文件
- `package.json` - 依赖和脚本配置
- `vercel.json` - 部署和重定向规则

## 开发工作流
1. 内容主要使用 Markdown 编写
2. 数据处理脚本处理批量操作
3. VitePress 构建静态站点
4. Vercel 处理部署和托管

## 依赖项
- **生产环境**: `xlsx` 用于 Excel 文件处理
- **开发环境**: `vitepress` 用于站点生成