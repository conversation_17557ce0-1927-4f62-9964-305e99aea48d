# 学习提升

收集各类优质的学习网站和教育平台，助力个人成长和技能提升。

## 在线教育平台

### 国际平台

#### Coursera
- **网址**: [https://www.coursera.org](https://www.coursera.org)
- **简介**: 全球知名的在线教育平台
- **特色**: 与顶尖大学合作、专业证书、学位课程
- **课程**: 计算机科学、商业、数据科学、人文社科

#### edX
- **网址**: [https://www.edx.org](https://www.edx.org)
- **简介**: 哈佛和MIT联合创办的在线教育平台
- **特色**: 免费课程、认证证书、微硕士项目
- **课程**: 工程技术、商业管理、人文艺术、科学

#### Udemy
- **网址**: [https://www.udemy.com](https://www.udemy.com)
- **简介**: 全球最大的在线学习市场
- **特色**: 课程丰富、实用技能、终身访问
- **课程**: 编程开发、设计、商业、个人发展

#### Khan Academy
- **网址**: [https://www.khanacademy.org](https://www.khanacademy.org)
- **简介**: 免费的世界级教育平台
- **特色**: 完全免费、个性化学习、练习系统
- **课程**: 数学、科学、编程、艺术、历史

### 国内平台

#### 中国大学MOOC
- **网址**: [https://www.icourse163.org](https://www.icourse163.org)
- **简介**: 网易与高教社合作的中文MOOC平台
- **特色**: 985高校课程、学分认证、免费学习
- **课程**: 理工科、人文社科、经管法、艺术设计

#### 学堂在线
- **网址**: [https://www.xuetangx.com](https://www.xuetangx.com)
- **简介**: 清华大学发起的中文MOOC平台
- **特色**: 清华课程、认证证书、企业培训
- **课程**: 计算机、工程、经管、理学、文学

#### 腾讯课堂
- **网址**: [https://ke.qq.com](https://ke.qq.com)
- **简介**: 腾讯推出的在线教育平台
- **特色**: 直播互动、职业技能、认证体系
- **课程**: IT互联网、设计创作、职业考证、兴趣生活

## 编程学习

### 代码练习平台

#### LeetCode
- **网址**: [https://leetcode.com](https://leetcode.com)
- **简介**: 全球知名的编程题库和竞赛平台
- **特色**: 算法题库、面试准备、编程竞赛
- **语言**: 支持多种编程语言、中英文界面

#### HackerRank
- **网址**: [https://www.hackerrank.com](https://www.hackerrank.com)
- **简介**: 编程技能评估和学习平台
- **特色**: 技能认证、招聘服务、竞赛活动
- **领域**: 算法、数据结构、AI、数据库

#### Codewars
- **网址**: [https://www.codewars.com](https://www.codewars.com)
- **简介**: 通过挑战提升编程技能的平台
- **特色**: 游戏化学习、社区讨论、技能等级
- **方式**: Kata挑战、同伴学习、代码审查

### 编程教程网站

#### freeCodeCamp
- **网址**: [https://www.freecodecamp.org](https://www.freecodecamp.org)
- **简介**: 免费的编程学习平台
- **特色**: 项目驱动、认证证书、开源社区
- **课程**: Web开发、数据科学、机器学习

#### Codecademy
- **网址**: [https://www.codecademy.com](https://www.codecademy.com)
- **简介**: 交互式编程学习平台
- **特色**: 实时编码、即时反馈、职业路径
- **语言**: Python、JavaScript、Java、C++等

#### W3Schools
- **网址**: [https://www.w3schools.com](https://www.w3schools.com)
- **简介**: Web技术学习参考网站
- **特色**: 简单易懂、在线编辑器、免费使用
- **技术**: HTML、CSS、JavaScript、SQL、Python

## 语言学习

### 英语学习

#### Duolingo
- **网址**: [https://www.duolingo.com](https://www.duolingo.com)
- **简介**: 游戏化的语言学习应用
- **特色**: 免费使用、游戏机制、多语言支持
- **方式**: 每日练习、连击系统、社区竞赛

#### BBC Learning English
- **网址**: [https://www.bbc.co.uk/learningenglish](https://www.bbc.co.uk/learningenglish)
- **简介**: BBC官方英语学习网站
- **特色**: 权威内容、时事英语、多媒体资源
- **内容**: 新闻英语、商务英语、发音练习

#### VOA Learning English
- **网址**: [https://learningenglish.voanews.com](https://learningenglish.voanews.com)
- **简介**: 美国之音英语学习频道
- **特色**: 慢速英语、新闻报道、文化介绍
- **级别**: 初级到高级、听说读写全面

### 多语言平台

#### Babbel
- **网址**: [https://www.babbel.com](https://www.babbel.com)
- **简介**: 专业的语言学习应用
- **特色**: 实用对话、语法解释、发音练习
- **语言**: 14种语言、欧洲语言为主

#### Busuu
- **网址**: [https://www.busuu.com](https://www.busuu.com)
- **简介**: AI驱动的语言学习平台
- **特色**: 个性化学习、母语者反馈、官方认证
- **功能**: 语音识别、写作练习、文化课程

## 技能学习

### 设计学习

#### Dribbble
- **网址**: [https://dribbble.com](https://dribbble.com)
- **简介**: 设计师作品展示和灵感平台
- **特色**: 高质量作品、设计趋势、招聘信息
- **内容**: UI/UX设计、平面设计、插画、品牌设计

#### Behance
- **网址**: [https://www.behance.net](https://www.behance.net)
- **简介**: Adobe旗下的创意作品展示平台
- **特色**: 作品集展示、创意发现、Adobe集成
- **领域**: 平面设计、摄影、插画、建筑、时尚

#### Design+Code
- **网址**: [https://designcode.io](https://designcode.io)
- **简介**: 设计和编程结合的学习平台
- **特色**: 实战项目、设计系统、原型制作
- **工具**: Figma、Framer、SwiftUI、React

### 数据科学

#### Kaggle
- **网址**: [https://www.kaggle.com](https://www.kaggle.com)
- **简介**: 全球最大的数据科学竞赛平台
- **特色**: 数据竞赛、免费数据集、学习课程
- **内容**: 机器学习、深度学习、数据分析

#### DataCamp
- **网址**: [https://www.datacamp.com](https://www.datacamp.com)
- **简介**: 专业的数据科学学习平台
- **特色**: 交互式学习、实战项目、技能评估
- **技术**: Python、R、SQL、Excel、Tableau

#### Towards Data Science
- **网址**: [https://towardsdatascience.com](https://towardsdatascience.com)
- **简介**: Medium上的数据科学出版物
- **特色**: 专业文章、实战案例、行业洞察
- **内容**: 机器学习、AI、数据工程、商业分析

## 学术资源

### 论文数据库

#### Google Scholar
- **网址**: [https://scholar.google.com](https://scholar.google.com)
- **简介**: Google的学术搜索引擎
- **特色**: 免费使用、引用统计、全文链接
- **内容**: 学术论文、专利、引用、作者信息

#### arXiv
- **网址**: [https://arxiv.org](https://arxiv.org)
- **简介**: 预印本论文存储库
- **特色**: 最新研究、免费访问、多学科覆盖
- **领域**: 物理、数学、计算机科学、生物学

#### ResearchGate
- **网址**: [https://www.researchgate.net](https://www.researchgate.net)
- **简介**: 科研人员社交网络平台
- **特色**: 论文分享、学术交流、合作机会
- **功能**: 论文发布、同行评议、研究指标

### 开放课程

#### MIT OpenCourseWare
- **网址**: [https://ocw.mit.edu](https://ocw.mit.edu)
- **简介**: MIT的开放式课程网站
- **特色**: 完全免费、课程完整、资料丰富
- **内容**: 工程、科学、管理、人文、艺术

#### Stanford Online
- **网址**: [https://online.stanford.edu](https://online.stanford.edu)
- **简介**: 斯坦福大学在线教育平台
- **特色**: 世界顶级、前沿技术、专业认证
- **课程**: 计算机科学、AI、商业、医学

#### Harvard Online Learning
- **网址**: [https://online-learning.harvard.edu](https://online-learning.harvard.edu)
- **简介**: 哈佛大学在线学习平台
- **特色**: 常春藤教育、多样化课程、灵活学习
- **领域**: 商业、教育、健康、技术、艺术

## 知识管理

### 笔记工具

#### Notion
- **网址**: [https://www.notion.so](https://www.notion.so)
- **简介**: 集笔记、数据库、协作于一体的工具
- **特色**: 模块化设计、强大定制、团队协作
- **功能**: 笔记、任务、数据库、wiki、项目管理

#### Obsidian
- **网址**: [https://obsidian.md](https://obsidian.md)
- **简介**: 基于链接的知识管理工具
- **特色**: 双向链接、图谱视图、插件生态
- **方式**: 本地存储、Markdown、可视化网络

#### Roam Research
- **网址**: [https://roamresearch.com](https://roamresearch.com)
- **简介**: 网络化思维的笔记工具
- **特色**: 双向链接、块引用、图数据库
- **理念**: 连接思维、知识图谱、非线性思考
